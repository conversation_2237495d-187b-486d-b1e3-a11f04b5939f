source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.3.7'

# Core gems
gem 'rails', '~> *******'
gem 'bootsnap', '>= 1.16.0', require: false unless RbConfig::CONFIG['host_os'] =~ /mswin|mingw|cygwin/
gem 'irb'
gem 'tzinfo-data'

# Database
gem 'mysql2', '>= 0.5.5', '< 0.6.0'
gem 'redis', '~> 4'
gem 'redis-namespace', '~> 1.11.0'
gem 'hiredis'
gem 'hiredis-client'

# Server and Background Jobs
gem 'puma', '<7'
gem 'sidekiq', '<7'
gem 'sidekiq-batch'
gem 'sidekiq-scheduler'
gem 'sidekiq-throttled'
gem 'sidekiq-unique-jobs', '<8'
gem "activejob-traffic_control"
gem 'sidekiq-assured-jobs', '~> 1.1.0'

# Authentication & Authorization
gem 'devise'
gem 'pundit'
gem 'omniauth-google-oauth2'
gem "omniauth-rails_csrf_protection"
gem 'rotp'

# ActiveAdmin
gem 'activeadmin', '< 3.3.0'
gem 'active_admin_import'
gem 'activeadmin_dark_color_scheme'
gem 'activeadmin-searchable_select'
gem 'activeadmin_dynamic_fields'
gem "font-awesome-sass"
gem 'sassc-rails', '2.1.2'

# API and Web Security
gem 'rack-attack'
gem 'rack-cors'
gem 'ransack'
gem 'paper_trail'

# AWS & Cloud Services
gem 'aws-sdk-lambda'
gem 'aws-sdk-s3', '<2'
gem 'faraday_middleware-aws-sigv4'
gem 'google-cloud-vision', '~> 1.3'
gem "google-cloud-translate-v3", "~> 0.9.0"
gem 'google-apis-sheets_v4'

# Search & Data Processing
gem 'opensearch-ruby'
gem 'searchkick'
gem 'activerecord-import'
gem 'caxlsx'

# Notification & Messaging
gem 'fcm'
gem 'firebase_dynamic_link'
gem 'sendgrid-ruby'
gem 'slack-notifier'
gem 'twilio-ruby'
gem 'nats-pure'

# HTTP & API Clients
gem 'httparty'
gem 'htmlcsstoimage-api'

# Utilities
gem "aasm"
gem 'concurrent-ruby', require: 'concurrent'
gem 'connection_pool'
gem 'fastimage'
gem 'hashid-rails'
gem 'honeybadger'
gem 'mixpanel-ruby'
gem 'newrelic_rpm'
gem 'nokogiri'
gem 'sshkit-sudo'
gem 'telephone_number'
gem 'ulid', '~> 1.4'
gem 'unicode-display_width'
gem 'nanoid'

group :development, :test do
  gem 'bcrypt_pbkdf', '>= 1.1.0', '< 2.0'
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'derailed_benchmarks'
  gem 'ed25519', '>= 1.3.0', '< 2.0'
  gem 'listen', '>= 3.8.0'
  gem 'net-ssh', '<8'
  gem 'rubocop', require: false
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.1.0'
  gem 'stackprof'
  gem 'i18n-tasks'
end

group :test do
  gem 'database_cleaner'
  gem 'factory_bot_rails', '<7'
  gem 'faker'
  gem 'rspec', '~> 3.12.0'
  gem 'rspec-rails', '~> 6.0.1'
  gem 'rspec-sidekiq'
  gem 'shoulda-matchers', '~> 5.3.0'
  gem 'simplecov', require: false
  gem 'webmock', '~> 3.18', '>= 3.18.1'
end