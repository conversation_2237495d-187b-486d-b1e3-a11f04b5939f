require 'rails_helper'

RSpec.describe Admin::RmFlowController, type: :controller do
  let(:admin_user) { create(:admin_user) }

  before do
    allow(controller).to receive(:current_admin_user).and_return(admin_user)
    allow(controller).to receive(:check_admin_user).and_return(true)
    allow(controller).to receive(:authorize).and_return(true)
  end

  describe "#default_leader_circles_of_a_party_v2" do
    let!(:party_circle) { create(:circle, level: :political_party, circle_type: :interest) }
    let(:party_id) { party_circle.id }
    let(:user) { create(:user, mla_constituency_id: 1001, mp_constituency_id: 2001) }
    
    # Create MLA and MP constituencies with proper hierarchy
    let!(:mp_constituency) { create(:circle, id: 2001, level: :mp_constituency, circle_type: :location, parent_circle_id: nil) }
    let!(:mla_constituency) { create(:circle, id: 1001, level: :mla_constituency, circle_type: :location, parent_circle_id: mp_constituency.id) }
    
    # Create MLA and MP leader circles with poster photos
    let!(:mla_leader) { create(:circle, id: 301, active: true, level: :political_leader, circle_type: :interest) }
    let!(:mp_leader) { create(:circle, id: 302, active: true, level: :political_leader, circle_type: :interest) }
    
    # Create additional leader circles for h2
    let!(:h2_leader_1) { create(:circle, id: 303, active: true, level: :political_leader, circle_type: :interest) }
    let!(:h2_leader_2) { create(:circle, id: 304, active: true, level: :political_leader, circle_type: :interest) }
    let!(:h2_leader_3) { create(:circle, id: 305, active: true, level: :political_leader, circle_type: :interest) }

    before do
      # Set up the user for the controller
      allow(controller).to receive(:set_user).and_return(true)
      controller.instance_variable_set(:@user, user)
      
      # Create poster photos for all leader circles
      [mla_leader, mp_leader, h2_leader_1, h2_leader_2, h2_leader_3].each do |leader|
        create(:circle_photo, 
               circle: leader, 
               photo: create(:photo), 
               photo_type: :poster, 
               photo_order: 1)
      end
      
      # Set up MLA relation (MLA -> MLA Leader -> Party)
      create(:circles_relation, 
             first_circle_id: mla_constituency.id, 
             second_circle_id: mla_leader.id, 
             relation: :MLA, 
             active: true)
      create(:circles_relation, 
             first_circle_id: mla_leader.id, 
             second_circle_id: party_circle.id, 
             relation: :Leader2Party, 
             active: true)
      
      # Set up MP relation (MP -> MP Leader -> Party)
      create(:circles_relation, 
             first_circle_id: mp_constituency.id, 
             second_circle_id: mp_leader.id, 
             relation: :MP, 
             active: true)
      create(:circles_relation, 
             first_circle_id: mp_leader.id, 
             second_circle_id: party_circle.id, 
             relation: :Leader2Party, 
             active: true)
      
      # Set up additional h2 leaders -> party relations
      [h2_leader_1, h2_leader_2, h2_leader_3].each do |leader|
        create(:circles_relation, 
               first_circle_id: leader.id, 
               second_circle_id: party_circle.id, 
               relation: :Leader2Party, 
               active: true)
      end
      
      # Mock the Circle.get_default_leader_circle_ids_for_party method
      allow(Circle).to receive(:get_default_leader_circle_ids_for_party)
        .with(party_id)
        .and_return([h2_leader_1.id, h2_leader_2.id, h2_leader_3.id])
    end

    it "returns bad_request status if poster_affiliated_party_id is blank" do
      get :default_leader_circles_of_a_party_v2, params: {}

      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)).to have_key("message")
    end

    it "returns bad_request status if poster_affiliated_party_id is empty string" do
      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: "" }

      expect(response).to have_http_status(:bad_request)
      expect(JSON.parse(response.body)).to have_key("message")
    end

    it "returns leader circles with correct types and priorities for valid poster_affiliated_party_id" do
      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      expect(result).to be_an(Array)
      expect(result.length).to eq(5) # 1 MLA (h1) + 1 MP + 3 other leaders (h2)
      
      # Check MLA leader (should be header_1)
      mla_result = result.find { |c| c["id"] == mla_leader.id }
      expect(mla_result["type"]).to eq("header_1")
      expect(mla_result["priority"]).to eq(1)
      
      # Check MP leader (should be header_2)
      mp_result = result.find { |c| c["id"] == mp_leader.id }
      expect(mp_result["type"]).to eq("header_2")
      expect(mp_result["priority"]).to eq(4) # MP is added after the 3 default leaders
      
      # Check h2 leaders
      h2_leader_1_result = result.find { |c| c["id"] == h2_leader_1.id }
      expect(h2_leader_1_result["type"]).to eq("header_2")
      expect(h2_leader_1_result["priority"]).to eq(1)
      
      h2_leader_2_result = result.find { |c| c["id"] == h2_leader_2.id }
      expect(h2_leader_2_result["type"]).to eq("header_2")
      expect(h2_leader_2_result["priority"]).to eq(2)
      
      h2_leader_3_result = result.find { |c| c["id"] == h2_leader_3.id }
      expect(h2_leader_3_result["type"]).to eq("header_2")
      expect(h2_leader_3_result["priority"]).to eq(3)
    end

    it "marks the first poster photo as selected and others as not selected" do
      # Add multiple poster photos to one of the leaders
      create(:circle_photo, 
             circle: mla_leader, 
             photo: create(:photo), 
             photo_type: :poster, 
             photo_order: 2)
      create(:circle_photo, 
             circle: mla_leader, 
             photo: create(:photo), 
             photo_type: :poster, 
             photo_order: 3)

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      
      mla_result = result.find { |c| c["id"] == mla_leader.id }
      expect(mla_result["poster_photos"]).to be_present
      expect(mla_result["poster_photos"].length).to eq(3)
      
      # First photo should be selected
      expect(mla_result["poster_photos"].first["selected"]).to be true
      
      # Other photos should not be selected
      expect(mla_result["poster_photos"][1]["selected"]).to be false
      expect(mla_result["poster_photos"][2]["selected"]).to be false
    end

    it "returns empty array if no mapping exists for the party ID" do
      allow(Circle).to receive(:get_default_leader_circle_ids_for_party)
        .with(999)
        .and_return([])

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: 999 }

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to eq([])
    end

    it "excludes circles that don't exist or are inactive" do
      # Make one of the h2 leaders inactive
      h2_leader_2.update!(active: false)
      
      # Add a non-existent circle ID to the mapping
      allow(Circle).to receive(:get_default_leader_circle_ids_for_party)
        .with(party_id)
        .and_return([h2_leader_1.id, h2_leader_2.id, h2_leader_3.id, 999])

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      
      # Should not include inactive or non-existent circles
      result_ids = result.map { |c| c["id"] }
      expect(result_ids).not_to include(h2_leader_2.id) # inactive
      expect(result_ids).not_to include(999) # non-existent
      expect(result_ids).to include(h2_leader_1.id, h2_leader_3.id) # active ones
    end

    it "excludes circles that have no poster photos" do
      # Remove poster photos from one leader
      h2_leader_2.circle_photos.where(photo_type: :poster).destroy_all

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      
      # Should not include circles without poster photos
      result_ids = result.map { |c| c["id"] }
      expect(result_ids).not_to include(h2_leader_2.id)
      expect(result_ids).to include(h2_leader_1.id, h2_leader_3.id)
    end

    it "handles case when user has no MLA or MP for the party" do
      # Create a user with different constituency IDs that don't have relations to the party
      user_without_relations = create(:user, mla_constituency_id: 9001, mp_constituency_id: 9002)
      controller.instance_variable_set(:@user, user_without_relations)

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      
      # Should only include the default leader circles (h2), no h1 circles
      expect(result.length).to eq(3) # Only h2 leaders
      result.each do |circle|
        expect(circle["type"]).to eq("header_2")
      end
    end

    it "handles case when only MLA exists but no MP for the party" do
      # Remove MP relation
      CirclesRelation.where(first_circle_id: mp_constituency.id, second_circle_id: mp_leader.id).destroy_all

      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)
      
      # Should include MLA as h1 and default leaders as h2
      h1_circles = result.select { |c| c["type"] == "header_1" }
      h2_circles = result.select { |c| c["type"] == "header_2" }
      
      expect(h1_circles.length).to eq(1)
      expect(h1_circles.first["id"]).to eq(mla_leader.id)
      expect(h2_circles.length).to eq(3) # Only default leaders, no MP
    end

    it "returns circles in the same JSON format as get_json_for_rm_layout_creation" do
      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)

      # Check that each circle has the expected structure
      result.each do |circle|
        expect(circle).to have_key("id")
        expect(circle).to have_key("name")
        expect(circle).to have_key("name_en")
        expect(circle).to have_key("short_name")
        expect(circle).to have_key("poster_photos")
        expect(circle).to have_key("sub_text")
        expect(circle).to have_key("type") # v2 specific
        expect(circle).to have_key("priority") # v2 specific
      end
    end

    it "preserves order within h1 and h2 groups based on priority" do
      get :default_leader_circles_of_a_party_v2, params: { poster_affiliated_party_id: party_id }

      expect(response).to have_http_status(:ok)
      result = JSON.parse(response.body)

      h1_circles = result.select { |c| c["type"] == "header_1" }.sort_by { |c| c["priority"] }
      h2_circles = result.select { |c| c["type"] == "header_2" }.sort_by { |c| c["priority"] }

      # H1 should have MLA with priority 1
      expect(h1_circles.length).to eq(1)
      expect(h1_circles.first["priority"]).to eq(1)
      expect(h1_circles.first["id"]).to eq(mla_leader.id)

      # H2 should have default leaders with priorities 1, 2, 3 and MP with priority 4
      expect(h2_circles.length).to eq(4)
      expect(h2_circles.map { |c| c["priority"] }).to eq([1, 2, 3, 4])
      
      # First three should be default leaders in order
      expect(h2_circles[0]["id"]).to eq(h2_leader_1.id)
      expect(h2_circles[1]["id"]).to eq(h2_leader_2.id)
      expect(h2_circles[2]["id"]).to eq(h2_leader_3.id)
      expect(h2_circles[3]["id"]).to eq(mp_leader.id) # MP comes last
    end
  end
end
